' Auto-generated Mock Data from Excel
' Generated on: 2025-08-21 23:58:14
' Source file: Model.xlsx
' Total Model Classes: 2
' All values are converted to string format
' ============================================================

' Model Class Field Usage Summary:
' YIC30Model: 6 fields (DutyOrgID, SemiYear, Semistry, objid, test001, test002)
' YIC30Q: 6 fields (DutyOrgNam, SemiYear, Semistry, objid, test001, test002)
' ============================================================

' Mock data for YIC30Model (Instance 1) - 6 fields
ttMyList.Add(New YIC30Model With {
    .objid = "1",
    .SemiYear = "55",
    .Semistry = "66",
    .DutyOrgID = "34.0",
    .test001 = "1",
    .test002 = "d"
})

' Mock data for YIC30Q (Instance 1) - 6 fields
ttMyList.Add(New YIC30Q With {
    .objid = "2",
    .SemiYear = "3",
    .Semistry = "88",
    .DutyOrgNam = "王",
    .test001 = "1",
    .test002 = "d"
})

' Mock data for YIC30Q (Instance 2) - 6 fields
ttMyList.Add(New YIC30Q With {
    .objid = "4",
    .SemiYear = "5",
    .Semistry = "99",
    .DutyOrgNam = "李",
    .test001 = "a",
    .test002 = "6"
})
