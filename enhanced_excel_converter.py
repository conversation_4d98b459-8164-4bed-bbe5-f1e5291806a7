import pandas as pd
import numpy as np
import os
from datetime import datetime

class ExcelToMockDataConverter:
    def __init__(self, excel_file="Model.xlsx"):
        self.excel_file = excel_file
        self.df = None
        
    def load_excel(self):
        """載入Excel檔案"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"✅ 成功載入Excel檔案: {self.excel_file}")
            print(f"📊 資料維度: {self.df.shape} (行數: {self.df.shape[0]}, 欄數: {self.df.shape[1]})")
            return True
        except FileNotFoundError:
            print(f"❌ 找不到檔案: {self.excel_file}")
            return False
        except Exception as e:
            print(f"❌ 載入Excel檔案時發生錯誤: {e}")
            return False
    
    def analyze_data(self):
        """分析Excel資料結構"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return

        print("\n📋 Excel檔案結構分析:")
        print("=" * 50)
        print(f"欄位名稱: {list(self.df.columns)}")
        print(f"\n各欄位的非空值數量:")
        for col in self.df.columns:
            non_null_count = self.df[col].count()
            total_count = len(self.df)
            print(f"  {col}: {non_null_count}/{total_count}")

        print(f"\n前3行資料預覽:")
        print(self.df.head(3).to_string())

        # 分析每個 Model Class 使用的欄位
        self.analyze_model_class_fields()

    def analyze_model_class_fields(self):
        """分析每個 Model Class 使用的欄位"""
        if self.df is None:
            return

        print("\n🔍 Model Class 欄位使用分析:")
        print("=" * 50)

        model_field_usage = {}

        # 遍歷每一行，統計每個 Model Class 使用的欄位
        for _, row in self.df.iterrows():
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None

            if not model_class or model_class.lower() == 'nan':
                continue

            if model_class not in model_field_usage:
                model_field_usage[model_class] = set()

            # 檢查每個欄位是否有值
            for col_name in self.df.columns[1:]:  # 從第二欄開始
                value = row[col_name]
                if not (pd.isna(value) or str(value).strip() == '' or str(value).strip().lower() == 'nan'):
                    model_field_usage[model_class].add(col_name)

        # 顯示分析結果
        for model_class, fields in model_field_usage.items():
            print(f"\n📌 {model_class}:")
            print(f"   使用欄位數量: {len(fields)}")
            print(f"   使用的欄位: {', '.join(sorted(fields))}")

        # 統計總覽
        total_fields = len(self.df.columns) - 1  # 扣除 Model Class 欄位
        print(f"\n📊 統計總覽:")
        print(f"   總可用欄位數: {total_fields}")
        print(f"   發現的 Model Class 數量: {len(model_field_usage)}")

        return model_field_usage

    def format_value(self, value, col_name):
        """格式化屬性值 - 所有值都轉換為字串格式"""
        # 跳過空值
        if pd.isna(value) or str(value).strip() == '' or str(value).strip().lower() == 'nan':
            return None

        # 所有值都轉換為字串格式（加引號）
        return f'"{str(value)}"'
    
    def get_available_model_classes(self):
        """取得所有可用的 Model Class"""
        if self.df is None:
            return []

        model_classes = set()
        for _, row in self.df.iterrows():
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            if model_class and model_class.lower() != 'nan':
                model_classes.add(model_class)

        return sorted(list(model_classes))

    def select_model_classes(self):
        """讓使用者選擇要轉換的 Model Class"""
        available_classes = self.get_available_model_classes()

        if not available_classes:
            print("❌ 沒有找到任何 Model Class")
            return None, None

        print(f"\n🎯 選擇轉換模式:")
        print("=" * 50)
        print("1. 轉換全部 Model Class")
        print("2. 選擇特定 Model Class")

        while True:
            try:
                choice = input("\n請選擇 (1 或 2): ").strip()
                if choice == "1":
                    return "all", "all_models_mock_data.vb"
                elif choice == "2":
                    break
                else:
                    print("❌ 請輸入 1 或 2")
            except KeyboardInterrupt:
                print("\n❌ 使用者取消操作")
                return None, None

        # 顯示可用的 Model Class
        print(f"\n📋 可用的 Model Class:")
        for i, model_class in enumerate(available_classes, 1):
            print(f"{i}. {model_class}")

        while True:
            try:
                choice = input(f"\n請選擇要轉換的 Model Class (1-{len(available_classes)}): ").strip()
                index = int(choice) - 1
                if 0 <= index < len(available_classes):
                    selected_class = available_classes[index]
                    output_file = f"{selected_class}_mock_data.vb"
                    return selected_class, output_file
                else:
                    print(f"❌ 請輸入 1 到 {len(available_classes)} 之間的數字")
            except ValueError:
                print("❌ 請輸入有效的數字")
            except KeyboardInterrupt:
                print("\n❌ 使用者取消操作")
                return None, None

    def generate_mock_data(self, output_file=None, include_comments=True, selected_model_class=None):
        """生成Mock Data"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return False

        # 如果沒有指定輸出檔案，使用預設名稱
        if output_file is None:
            if selected_model_class and selected_model_class != "all":
                output_file = f"{selected_model_class}_mock_data.vb"
            else:
                output_file = "all_models_mock_data.vb"

        if selected_model_class and selected_model_class != "all":
            print(f"\n🔄 開始生成 {selected_model_class} 的 Mock Data...")
        else:
            print(f"\n🔄 開始生成全部 Mock Data...")

        # 先分析欄位使用情況
        model_field_usage = {}
        for _, row in self.df.iterrows():
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            if not model_class or model_class.lower() == 'nan':
                continue

            # 如果指定了特定的 Model Class，只處理該類別
            if selected_model_class and selected_model_class != "all" and model_class != selected_model_class:
                continue

            if model_class not in model_field_usage:
                model_field_usage[model_class] = set()

            for col_name in self.df.columns[1:]:
                value = row[col_name]
                if not (pd.isna(value) or str(value).strip() == '' or str(value).strip().lower() == 'nan'):
                    model_field_usage[model_class].add(col_name)

        mock_lines = []

        # 檔案標頭
        if include_comments:
            conversion_mode = "All Model Classes" if (selected_model_class is None or selected_model_class == "all") else f"Selected: {selected_model_class}"

            mock_lines.extend([
                "' Auto-generated Mock Data from Excel",
                f"' Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"' Source file: {self.excel_file}",
                f"' Conversion mode: {conversion_mode}",
                f"' Total Model Classes: {len(model_field_usage)}",
                "' All values are converted to string format",
                "' " + "=" * 60,
                ""
            ])

            # 添加欄位使用摘要
            mock_lines.append("' Model Class Field Usage Summary:")
            for model_class, fields in model_field_usage.items():
                mock_lines.append(f"' {model_class}: {len(fields)} fields ({', '.join(sorted(fields))})")
            mock_lines.append("' " + "=" * 60)
            mock_lines.append("")

        # 處理每一行資料
        processed_count = 0
        model_instance_count = {}

        for _, row in self.df.iterrows():
            # 取得Model Class名稱（第一欄）
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None

            if not model_class or model_class.lower() == 'nan':
                continue

            # 如果指定了特定的 Model Class，只處理該類別
            if selected_model_class and selected_model_class != "all" and model_class != selected_model_class:
                continue

            # 統計每個 Model Class 的實例數量
            if model_class not in model_instance_count:
                model_instance_count[model_class] = 0
            model_instance_count[model_class] += 1

            # 收集屬性
            properties = []
            for col_name in self.df.columns[1:]:  # 從第二欄開始
                formatted_value = self.format_value(row[col_name], col_name)
                if formatted_value is not None:
                    properties.append(f".{col_name} = {formatted_value}")

            # 生成Mock Data條目
            if properties:
                if include_comments:
                    instance_num = model_instance_count[model_class]
                    mock_lines.append(f"' Mock data for {model_class} (Instance {instance_num}) - {len(properties)} fields")

                mock_lines.append(f"ttMyList.Add(New {model_class} With {{")

                # 添加屬性
                for i, prop in enumerate(properties):
                    if i == len(properties) - 1:
                        mock_lines.append(f"    {prop}")
                    else:
                        mock_lines.append(f"    {prop},")

                mock_lines.append("})")
                mock_lines.append("")
                processed_count += 1
        
        # 寫入檔案
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(mock_lines))

            print(f"✅ Mock Data已成功生成: {output_file}")
            print(f"📄 總共生成 {len(mock_lines)} 行程式碼")
            print(f"🎯 處理了 {processed_count} 個 Mock Data 實例")

            # 顯示每個 Model Class 的實例統計
            print(f"\n📊 Model Class 實例統計:")
            for model_class, count in model_instance_count.items():
                fields_used = len(model_field_usage.get(model_class, set()))
                print(f"   {model_class}: {count} 個實例, 使用 {fields_used} 個欄位")

            return True

        except Exception as e:
            print(f"❌ 儲存檔案時發生錯誤: {e}")
            return False
    
    def preview_mock_data(self, lines=15):
        """預覽生成的Mock Data"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return
            
        print(f"\n🔍 Mock Data預覽 (前{lines}行):")
        print("=" * 60)
        
        # 暫時生成Mock Data用於預覽
        mock_lines = []
        count = 0
        
        for _, row in self.df.iterrows():
            if count >= lines:
                break

            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            if not model_class or model_class.lower() == 'nan':
                continue

            properties = []
            for col_name in self.df.columns[1:]:
                formatted_value = self.format_value(row[col_name], col_name)
                if formatted_value is not None:
                    properties.append(f".{col_name} = {formatted_value}")

            if properties:
                mock_lines.append(f"' {model_class} - {len(properties)} fields (all string values)")
                mock_lines.append(f"ttMyList.Add(New {model_class} With {{")
                for i, prop in enumerate(properties):
                    if i == len(properties) - 1:
                        mock_lines.append(f"    {prop}")
                    else:
                        mock_lines.append(f"    {prop},")
                mock_lines.append("})")
                mock_lines.append("")
                count += len(properties) + 4
        
        for line in mock_lines[:lines]:
            print(line)

        if len(mock_lines) > lines:
            print("...")

    def preview_mock_data_for_class(self, target_model_class, lines=15):
        """預覽特定 Model Class 的 Mock Data"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return

        print(f"\n🔍 {target_model_class} Mock Data預覽 (前{lines}行):")
        print("=" * 60)

        mock_lines = []
        count = 0

        for _, row in self.df.iterrows():
            if count >= lines:
                break

            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            if not model_class or model_class.lower() == 'nan' or model_class != target_model_class:
                continue

            properties = []
            for col_name in self.df.columns[1:]:
                formatted_value = self.format_value(row[col_name], col_name)
                if formatted_value is not None:
                    properties.append(f".{col_name} = {formatted_value}")

            if properties:
                mock_lines.append(f"' {model_class} - {len(properties)} fields (all string values)")
                mock_lines.append(f"ttMyList.Add(New {model_class} With {{")
                for i, prop in enumerate(properties):
                    if i == len(properties) - 1:
                        mock_lines.append(f"    {prop}")
                    else:
                        mock_lines.append(f"    {prop},")
                mock_lines.append("})")
                mock_lines.append("")
                count += len(properties) + 4

        if not mock_lines:
            print(f"❌ 沒有找到 {target_model_class} 的資料")
            return

        for line in mock_lines[:lines]:
            print(line)

        if len(mock_lines) > lines:
            print("...")

def main():
    """主程式"""
    print("🚀 Excel to VB.NET Mock Data Converter v2.0")
    print("=" * 50)

    # 建立轉換器
    converter = ExcelToMockDataConverter("Model.xlsx")

    # 載入Excel
    if not converter.load_excel():
        return

    # 分析資料
    converter.analyze_data()

    # 讓使用者選擇轉換模式
    selected_model_class, output_file = converter.select_model_classes()

    if selected_model_class is None:
        print("❌ 未選擇任何轉換模式，程式結束")
        return

    # 預覽Mock Data
    print(f"\n🔍 預覽將要生成的 Mock Data:")
    if selected_model_class == "all":
        converter.preview_mock_data()
    else:
        print(f"   僅顯示 {selected_model_class} 的預覽")
        converter.preview_mock_data_for_class(selected_model_class)

    # 生成完整的Mock Data
    print("\n" + "=" * 50)
    success = converter.generate_mock_data(output_file, include_comments=True, selected_model_class=selected_model_class)

    if success:
        print("🎉 轉換完成！")
        print(f"📁 請檢查生成的檔案: {output_file}")
    else:
        print("❌ 轉換失敗")

if __name__ == "__main__":
    main()
