import pandas as pd
import numpy as np
import os
from datetime import datetime

class ExcelToMockDataConverter:
    def __init__(self, excel_file="Model.xlsx"):
        self.excel_file = excel_file
        self.df = None
        
    def load_excel(self):
        """載入Excel檔案"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"✅ 成功載入Excel檔案: {self.excel_file}")
            print(f"📊 資料維度: {self.df.shape} (行數: {self.df.shape[0]}, 欄數: {self.df.shape[1]})")
            return True
        except FileNotFoundError:
            print(f"❌ 找不到檔案: {self.excel_file}")
            return False
        except Exception as e:
            print(f"❌ 載入Excel檔案時發生錯誤: {e}")
            return False
    
    def analyze_data(self):
        """分析Excel資料結構"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return
            
        print("\n📋 Excel檔案結構分析:")
        print("=" * 50)
        print(f"欄位名稱: {list(self.df.columns)}")
        print(f"\n各欄位的非空值數量:")
        for col in self.df.columns:
            non_null_count = self.df[col].count()
            total_count = len(self.df)
            print(f"  {col}: {non_null_count}/{total_count}")
        
        print(f"\n前3行資料預覽:")
        print(self.df.head(3).to_string())
    
    def format_value(self, value, col_name):
        """格式化屬性值"""
        # 跳過空值
        if pd.isna(value) or str(value).strip() == '' or str(value).strip().lower() == 'nan':
            return None
            
        # 字串類型加引號
        if isinstance(value, str):
            return f'"{value}"'
        # 數字類型直接轉換
        elif isinstance(value, (int, float)):
            if pd.isna(value):
                return None
            # 如果是整數，去掉小數點
            if isinstance(value, float) and value.is_integer():
                return str(int(value))
            return str(value)
        else:
            return f'"{str(value)}"'
    
    def generate_mock_data(self, output_file="mock_data.vb", include_comments=True):
        """生成Mock Data"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return False
            
        mock_lines = []
        
        # 檔案標頭
        if include_comments:
            mock_lines.extend([
                "' Auto-generated Mock Data from Excel",
                f"' Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"' Source file: {self.excel_file}",
                "' " + "=" * 60,
                ""
            ])
        
        # 處理每一行資料
        for index, row in self.df.iterrows():
            # 取得Model Class名稱（第一欄）
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            
            if not model_class or model_class.lower() == 'nan':
                continue
            
            # 收集屬性
            properties = []
            for col_name in self.df.columns[1:]:  # 從第二欄開始
                formatted_value = self.format_value(row[col_name], col_name)
                if formatted_value is not None:
                    properties.append(f".{col_name} = {formatted_value}")
            
            # 生成Mock Data條目
            if properties:
                if include_comments:
                    mock_lines.append(f"' Mock data for {model_class}")
                
                mock_lines.append(f"ttMyList.Add(New {model_class} With {{")
                
                # 添加屬性
                for i, prop in enumerate(properties):
                    if i == len(properties) - 1:
                        mock_lines.append(f"    {prop}")
                    else:
                        mock_lines.append(f"    {prop},")
                
                mock_lines.append("})")
                mock_lines.append("")
        
        # 寫入檔案
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(mock_lines))
            
            print(f"✅ Mock Data已成功生成: {output_file}")
            print(f"📄 總共生成 {len(mock_lines)} 行程式碼")
            return True
            
        except Exception as e:
            print(f"❌ 儲存檔案時發生錯誤: {e}")
            return False
    
    def preview_mock_data(self, lines=15):
        """預覽生成的Mock Data"""
        if self.df is None:
            print("❌ 請先載入Excel資料")
            return
            
        print(f"\n🔍 Mock Data預覽 (前{lines}行):")
        print("=" * 60)
        
        # 暫時生成Mock Data用於預覽
        mock_lines = []
        count = 0
        
        for index, row in self.df.iterrows():
            if count >= lines:
                break
                
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            if not model_class or model_class.lower() == 'nan':
                continue
            
            properties = []
            for col_name in self.df.columns[1:]:
                formatted_value = self.format_value(row[col_name], col_name)
                if formatted_value is not None:
                    properties.append(f".{col_name} = {formatted_value}")
            
            if properties:
                mock_lines.append(f"ttMyList.Add(New {model_class} With {{")
                for i, prop in enumerate(properties):
                    if i == len(properties) - 1:
                        mock_lines.append(f"    {prop}")
                    else:
                        mock_lines.append(f"    {prop},")
                mock_lines.append("})")
                mock_lines.append("")
                count += len(properties) + 3
        
        for line in mock_lines[:lines]:
            print(line)
        
        if len(mock_lines) > lines:
            print("...")

def main():
    """主程式"""
    print("🚀 Excel to VB.NET Mock Data Converter")
    print("=" * 50)
    
    # 建立轉換器
    converter = ExcelToMockDataConverter("Model.xlsx")
    
    # 載入Excel
    if not converter.load_excel():
        return
    
    # 分析資料
    converter.analyze_data()
    
    # 預覽Mock Data
    converter.preview_mock_data()
    
    # 生成完整的Mock Data
    print("\n" + "=" * 50)
    success = converter.generate_mock_data("generated_mock_data.vb")
    
    if success:
        print("🎉 轉換完成！")
        print("📁 請檢查生成的檔案: generated_mock_data.vb")
    else:
        print("❌ 轉換失敗")

if __name__ == "__main__":
    main()
