# Excel to VB.NET Mock Data Converter

這個Python程式可以將Excel檔案中的資料轉換成VB.NET MVC專案可用的Mock Data格式。

## 功能特色

- 🔄 自動讀取Excel檔案並轉換為VB.NET Mock Data格式
- 📊 智能分析Excel資料結構
- 🚫 自動跳過空值欄位
- 📝 生成格式化的VB.NET程式碼
- 🔍 提供資料預覽功能
- 📅 包含生成時間戳記

## 檔案說明

1. **simple_excel_converter.py** - 基本版本轉換器
2. **enhanced_excel_converter.py** - 增強版本轉換器（推薦使用）
3. **excel_to_mock_data.py** - 完整功能版本

## Excel檔案格式要求

您的Excel檔案應該按照以下格式組織：

```
| Model Class | objid | AAID | SemiYear | Semistry | DutyOrgID | DutyOrgName |
|-------------|-------|------|----------|----------|-----------|-------------|
| YIC30Model  | 1     |      | 55       | 66       | 34        |             |
| YIC30Q      | 2     |      | 3        | 88       |           | 王          |
| YIC30Q      | 4     |      | 5        | 99       |           | 李          |
```

- **第一欄**: Model Class名稱
- **其他欄**: 屬性欄位
- **橫排**: 欄位名稱
- **直行**: 不同的model class實例

## 使用方法

### 1. 安裝必要套件

```bash
pip install pandas openpyxl
```

### 2. 執行轉換程式

```bash
python enhanced_excel_converter.py
```

### 3. 檢查輸出檔案

程式會生成 `generated_mock_data.vb` 檔案，內容格式如下：

```vb
' Auto-generated Mock Data from Excel
' Generated on: 2025-08-21 23:41:07
' Source file: Model.xlsx
' ============================================================

' Mock data for YIC30Model
ttMyList.Add(New YIC30Model With {
    .objid = 1,
    .SemiYear = 55,
    .Semistry = 66,
    .DutyOrgID = 34
})

' Mock data for YIC30Q
ttMyList.Add(New YIC30Q With {
    .objid = 2,
    .SemiYear = 3,
    .Semistry = 88,
    .DutyOrgNam = "王"
})
```

## 程式特點

### 智能資料處理
- 自動跳過空值（NaN、空字串）
- 智能判斷資料類型（字串加引號，數字不加引號）
- 浮點數如果是整數會自動轉換為整數格式

### 錯誤處理
- 檔案不存在時會顯示錯誤訊息
- 資料格式錯誤時會提供詳細錯誤資訊
- 支援中文檔案名稱和內容

### 輸出格式
- 符合VB.NET語法規範
- 包含註解說明
- 自動處理最後一個屬性不加逗號的格式

## 自訂設定

您可以修改程式中的以下參數：

```python
# 修改輸入檔案名稱
converter = ExcelToMockDataConverter("您的檔案名稱.xlsx")

# 修改輸出檔案名稱
converter.generate_mock_data("您的輸出檔案.vb")

# 控制是否包含註解
converter.generate_mock_data("output.vb", include_comments=False)
```

## 故障排除

### 常見問題

1. **找不到檔案錯誤**
   - 確認Excel檔案在同一目錄下
   - 檢查檔案名稱是否正確

2. **編碼問題**
   - 確保Excel檔案使用UTF-8編碼
   - 中文內容應該能正常處理

3. **套件安裝問題**
   ```bash
   pip install --upgrade pandas openpyxl
   ```

### 支援的資料類型
- 字串 (String)
- 整數 (Integer)
- 浮點數 (Float)
- 空值會被自動跳過

## 範例輸出

根據您提供的範例，程式會生成類似以下格式的Mock Data：

```vb
ttMyList.Add(New YIC30ResModel With {
    .objid = "R001",
    .DutyOrgID = "YIC20",
    .DutyOrgName = "貴重儀器中心",
    .SubClassEx = "B0301",
    .Core = "蛋白質體學核心"
})
```

## 版本資訊

- 版本: 1.0
- 支援Python: 3.6+
- 相依套件: pandas, openpyxl
- 作者: AI Assistant
- 更新日期: 2025-08-21
