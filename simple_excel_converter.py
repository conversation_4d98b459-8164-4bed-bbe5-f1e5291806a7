import pandas as pd
import numpy as np

def convert_excel_to_mock_data(excel_file="Model.xlsx", output_file="mock_data.vb"):
    """
    將Excel檔案轉換為VB.NET Mock Data格式
    
    Excel格式假設：
    - 第一欄：Model Class名稱
    - 其他欄：屬性欄位
    - 橫排：欄位名稱
    - 直行：不同的model class實例
    """
    
    try:
        # 讀取Excel檔案
        print(f"正在讀取 {excel_file}...")
        df = pd.read_excel(excel_file)
        
        print(f"Excel資料載入成功！")
        print(f"資料維度: {df.shape}")
        print(f"欄位名稱: {list(df.columns)}")
        print("\n前幾行資料預覽:")
        print(df.head())
        
        # 生成Mock Data
        mock_data_lines = []
        mock_data_lines.append("' Auto-generated Mock Data from Excel")
        mock_data_lines.append("' Generated by Python Excel Converter")
        mock_data_lines.append("' " + "="*60)
        mock_data_lines.append("")
        
        # 遍歷每一行資料
        for index, row in df.iterrows():
            # 取得Model Class名稱（假設在第一欄）
            model_class = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
            
            if not model_class or model_class == 'nan':
                continue
                
            # 收集非空的屬性
            properties = []
            
            # 從第二欄開始處理屬性
            for col_name in df.columns[1:]:
                value = row[col_name]
                
                # 跳過空值
                if pd.isna(value) or str(value).strip() == '' or str(value).strip().lower() == 'nan':
                    continue
                
                # 格式化屬性值
                if isinstance(value, str):
                    formatted_value = f'"{value}"'
                elif isinstance(value, (int, float)):
                    if pd.isna(value):
                        continue
                    formatted_value = str(value)
                else:
                    formatted_value = f'"{str(value)}"'
                
                properties.append(f".{col_name} = {formatted_value}")
            
            # 如果有屬性，生成Mock Data條目
            if properties:
                mock_data_lines.append(f"' Mock data for {model_class}")
                mock_data_lines.append(f"ttMyList.Add(New {model_class} With {{")
                
                # 添加屬性，最後一個不加逗號
                for i, prop in enumerate(properties):
                    if i == len(properties) - 1:
                        mock_data_lines.append(f"    {prop}")
                    else:
                        mock_data_lines.append(f"    {prop},")
                
                mock_data_lines.append("})")
                mock_data_lines.append("")
        
        # 將結果寫入檔案
        mock_data_content = "\n".join(mock_data_lines)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(mock_data_content)
        
        print(f"\nMock Data已成功生成並儲存到: {output_file}")
        
        # 顯示生成的內容預覽
        print("\n生成的Mock Data預覽:")
        print("="*60)
        preview_lines = mock_data_lines[:20]  # 顯示前20行
        for line in preview_lines:
            print(line)
        
        if len(mock_data_lines) > 20:
            print("...")
            print(f"(總共 {len(mock_data_lines)} 行)")
        
        return True
        
    except FileNotFoundError:
        print(f"錯誤: 找不到檔案 {excel_file}")
        return False
    except Exception as e:
        print(f"轉換過程中發生錯誤: {e}")
        return False

def analyze_excel_structure(excel_file="Model.xlsx"):
    """
    分析Excel檔案結構
    """
    try:
        df = pd.read_excel(excel_file)
        print("Excel檔案結構分析:")
        print("="*40)
        print(f"總行數: {len(df)}")
        print(f"總欄數: {len(df.columns)}")
        print(f"欄位名稱: {list(df.columns)}")
        print("\n各欄位的資料類型:")
        print(df.dtypes)
        print("\n各欄位的非空值數量:")
        print(df.count())
        print("\n前5行資料:")
        print(df.head())
        
    except Exception as e:
        print(f"分析Excel檔案時發生錯誤: {e}")

if __name__ == "__main__":
    print("Excel to Mock Data Converter")
    print("="*40)
    
    # 先分析Excel結構
    analyze_excel_structure()
    
    print("\n" + "="*40)
    
    # 執行轉換
    success = convert_excel_to_mock_data()
    
    if success:
        print("\n轉換完成！請檢查生成的 mock_data.vb 檔案。")
    else:
        print("\n轉換失敗，請檢查Excel檔案格式。")
